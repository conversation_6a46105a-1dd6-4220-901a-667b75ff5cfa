import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { message } from "antd";
import * as permissionManagementService from "../../services/permissionManagementService";

// 获取权限列表
export const fetchPermissionList = createAsyncThunk(
  "permissionManagement/fetchPermissionList",
  async (params, { rejectWithValue }) => {
    try {
      const response = await permissionManagementService.getPermissionList(
        params
      );
      return response;
    } catch (error) {
      message.error("获取权限列表失败");
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// 获取权限详情
export const fetchPermissionDetail = createAsyncThunk(
  "permissionManagement/fetchPermissionDetail",
  async (id, { rejectWithValue }) => {
    try {
      const response = await permissionManagementService.getPermissionDetail(
        id
      );
      return response;
    } catch (error) {
      message.error("获取权限详情失败");
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// 添加权限
export const addPermission = createAsyncThunk(
  "permissionManagement/addPermission",
  async (data, { rejectWithValue, dispatch }) => {
    try {
      const response = await permissionManagementService.addPermission(data);
      message.success("添加权限成功");
      dispatch(fetchPermissionList({}));
      return response;
    } catch (error) {
      message.error("添加权限失败");
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// 更新权限
export const updatePermission = createAsyncThunk(
  "permissionManagement/updatePermission",
  async ({ id, data }, { rejectWithValue, dispatch }) => {
    try {
      const response = await permissionManagementService.updatePermission(
        id,
        data
      );
      message.success("更新权限成功");
      dispatch(fetchPermissionList({}));
      return response;
    } catch (error) {
      message.error("更新权限失败");
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// 删除权限
export const deletePermission = createAsyncThunk(
  "permissionManagement/deletePermission",
  async (id, { rejectWithValue, dispatch }) => {
    try {
      const response = await permissionManagementService.deletePermission(id);
      message.success("删除权限成功");
      dispatch(fetchPermissionList({}));
      return response;
    } catch (error) {
      message.error("删除权限失败");
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// 批量删除权限
export const batchDeletePermissions = createAsyncThunk(
  "permissionManagement/batchDeletePermissions",
  async (ids, { rejectWithValue, dispatch }) => {
    try {
      const response = await permissionManagementService.batchDeletePermissions(
        ids
      );
      message.success(`已成功删除 ${ids.length} 个权限`);
      dispatch(clearSelectedRowKeys());
      dispatch(fetchPermissionList({}));
      return response;
    } catch (error) {
      message.error("批量删除权限失败");
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// 获取所有员工列表（用于筛选器）
export const fetchAllStaffList = createAsyncThunk(
  "permissionManagement/fetchAllStaffList",
  async (_, { rejectWithValue }) => {
    try {
      const response = await permissionManagementService.getAllStaffList();
      return response;
    } catch (error) {
      console.error("获取员工列表失败:", error);
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// 获取所有资源列表（用于筛选器）
export const fetchAllResourcesList = createAsyncThunk(
  "permissionManagement/fetchAllResourcesList",
  async (_, { rejectWithValue }) => {
    try {
      const response = await permissionManagementService.getAllResourcesList();
      return response;
    } catch (error) {
      console.error("获取资源列表失败:", error);
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

const initialState = {
  permissionList: [],
  permissionDetail: null,
  pagination: {
    current: 1,
    pageSize: 20,
    total: 0,
  },
  loading: false,
  deleteData: {
    loading: false,
    error: null,
  },
  batchDeleteData: {
    loading: false,
    error: null,
  },
  filters: {
    staffFilter: "全部员工",
    resourceFilter: "全部资源",
  },
  selectedRowKeys: [],
  // 筛选选项数据
  filterOptions: {
    staffList: [],
    resourceList: [],
    loading: false,
  },
};

const permissionManagementSlice = createSlice({
  name: "permissionManagement",
  initialState,
  reducers: {
    setFilters(state, action) {
      state.filters = { ...state.filters, ...action.payload };
    },
    setPagination(state, action) {
      state.pagination = { ...state.pagination, ...action.payload };
    },
    setSelectedRowKeys(state, action) {
      state.selectedRowKeys = action.payload;
    },
    clearSelectedRowKeys(state) {
      state.selectedRowKeys = [];
    },
  },
  extraReducers: (builder) => {
    builder
      // 获取权限列表
      .addCase(fetchPermissionList.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchPermissionList.fulfilled, (state, action) => {
        state.loading = false;

        // 初始化空数组，防止undefined错误
        state.permissionList = [];

        // 使用静态测试数据
        const testData = [
          {
            id: 525,
            manageId: 860,
            username: "admin001",
            resourceId: 441,
            resourceMenuPath: "/system/user",
            resourceMenuName: "用户管理",
            addButtons: "add,edit",
            addFields: "username,email,phone",
            addUrls: "/api/users/create,/api/users/update",
            removeButtons: "",
            removeFields: "",
            removeUrls: "",
          },
          {
            id: 526,
            manageId: 861,
            username: "finance001",
            resourceId: 442,
            resourceMenuPath: "/finance/payment",
            resourceMenuName: "支付管理",
            addButtons: "view,export",
            addFields: "amount,status,time",
            addUrls: "/api/finance/list,/api/finance/export",
            removeButtons: "delete,approve",
            removeFields: "bankAccount",
            removeUrls: "/api/finance/delete",
          },
          {
            id: 527,
            manageId: 862,
            username: "service001",
            resourceId: 443,
            resourceMenuPath: "/service/ticket",
            resourceMenuName: "工单管理",
            addButtons: "reply,close",
            addFields: "content,status",
            addUrls: "/api/ticket/reply,/api/ticket/close",
            removeButtons: "delete",
            removeFields: "customerInfo",
            removeUrls: "/api/ticket/delete",
          },
        ];

        state.permissionList = testData;
        state.pagination.total = testData.length;
        state.pagination.current = 1;
        state.pagination.pageSize = 20;
      })
      .addCase(fetchPermissionList.rejected, (state) => {
        state.loading = false;
      })

      // 批量删除权限
      .addCase(batchDeletePermissions.pending, (state) => {
        state.loading = true;
        state.batchDeleteData.loading = true;
        state.batchDeleteData.error = null;
      })
      .addCase(batchDeletePermissions.fulfilled, (state) => {
        state.loading = false;
        state.batchDeleteData.loading = false;
        // 在reducer中也重置选中状态，确保无论如何都会清除选中
        state.selectedRowKeys = [];
      })
      .addCase(batchDeletePermissions.rejected, (state, action) => {
        state.loading = false;
        state.batchDeleteData.loading = false;
        state.batchDeleteData.error = action.payload;
      })

      // 获取权限详情
      .addCase(fetchPermissionDetail.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchPermissionDetail.fulfilled, (state, action) => {
        state.loading = false;
        const { data } = action.payload;
        if (data) {
          state.permissionDetail = data.data;
        }
      })
      .addCase(fetchPermissionDetail.rejected, (state) => {
        state.loading = false;
      })

      // 添加权限
      .addCase(addPermission.pending, (state) => {
        state.loading = true;
      })
      .addCase(addPermission.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(addPermission.rejected, (state) => {
        state.loading = false;
      })

      // 删除单个权限
      .addCase(deletePermission.pending, (state) => {
        state.deleteData.loading = true;
        state.deleteData.error = null;
      })
      .addCase(deletePermission.fulfilled, (state) => {
        state.deleteData.loading = false;
        // 不需要手动更新列表，因为会重新获取数据
      })
      .addCase(deletePermission.rejected, (state, action) => {
        state.deleteData.loading = false;
        state.deleteData.error = action.payload;
      })

      // 更新权限
      .addCase(updatePermission.pending, (state) => {
        state.loading = true;
      })
      .addCase(updatePermission.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(updatePermission.rejected, (state) => {
        state.loading = false;
      })

      // 获取员工列表
      .addCase(fetchAllStaffList.pending, (state) => {
        state.filterOptions.loading = true;
      })
      .addCase(fetchAllStaffList.fulfilled, (state, action) => {
        state.filterOptions.loading = false;
        state.filterOptions.staffList = action.payload?.data || [];
      })
      .addCase(fetchAllStaffList.rejected, (state) => {
        state.filterOptions.loading = false;
      })

      // 获取资源列表
      .addCase(fetchAllResourcesList.pending, (state) => {
        state.filterOptions.loading = true;
      })
      .addCase(fetchAllResourcesList.fulfilled, (state, action) => {
        state.filterOptions.loading = false;
        state.filterOptions.resourceList = action.payload?.data || [];
      })
      .addCase(fetchAllResourcesList.rejected, (state) => {
        state.filterOptions.loading = false;
      });
  },
});

export const {
  setFilters,
  setPagination,
  setSelectedRowKeys,
  clearSelectedRowKeys,
} = permissionManagementSlice.actions;

export default permissionManagementSlice.reducer;
