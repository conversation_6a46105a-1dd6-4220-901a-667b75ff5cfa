import axios from "../api/axios";

/**
 * 获取员工列表
 * @param {Object} params - 查询参数
 * @returns {Promise} - 返回员工列表数据
 */
export const getStaffList = (params = {}) => {
  // 确保必填参数存在
  const queryParams = {
    page: 1,
    pageSize: 20,
    ...params
  };

  return axios.get("/manage/list", { params: queryParams });
};

/**
 * 获取员工详情
 * @param {number} id - 员工ID
 * @returns {Promise} - 返回员工详情数据
 */
export const getStaffDetail = (id) => {
  return axios.get(`/manage/${id}`);
};

/**
 * 添加员工
 * @param {Object} staffData - 员工数据
 * @returns {Promise} - 返回添加结果
 */
export const addStaff = (staffData) => {
  return axios.post("/manage", staffData);
};

/**
 * 更新员工信息
 * @param {number} id - 员工ID
 * @param {Object} staffData - 员工数据
 * @returns {Promise} - 返回更新结果
 */
export const updateStaff = (id, staffData) => {
  return axios.put(`/manage/${id}`, staffData);
};

/**
 * 删除员工
 * @param {number} id - 员工ID
 * @returns {Promise} - 返回删除结果
 */
export const deleteStaff = (id) => {
  return axios.delete(`/manage/${id}`);
};

/**
 * 批量删除员工
 * @param {Array} ids - 员工ID数组
 * @returns {Promise} - 返回批量删除结果
 */
export const batchDeleteStaff = (ids) => {
  return axios.delete("/manage/batch", {
    params: { manageIds: ids },
    paramsSerializer: params => {
      // 处理数组参数，使用重复参数名的形式：manageIds=1&manageIds=2&manageIds=3
      const searchParams = new URLSearchParams();
      Object.keys(params).forEach(key => {
        if (Array.isArray(params[key])) {
          params[key].forEach(value => searchParams.append(key, value));
        } else {
          searchParams.append(key, params[key]);
        }
      });
      return searchParams.toString();
    }
  });
};

/**
 * 批量更新员工状态
 * @param {Array} ids - 员工ID数组
 * @param {string} status - 状态值
 * @returns {Promise} - 返回批量更新状态结果
 */
export const batchUpdateStaffStatus = (ids, status) => {
  return axios.put("/manage/batch/status", null, {
    params: { manageIds: ids, status },
    paramsSerializer: params => {
      // 处理数组参数，使用重复参数名的形式：manageIds=1&manageIds=2&manageIds=3
      const searchParams = new URLSearchParams();
      Object.keys(params).forEach(key => {
        if (Array.isArray(params[key])) {
          params[key].forEach(value => searchParams.append(key, value));
        } else {
          searchParams.append(key, params[key]);
        }
      });
      return searchParams.toString();
    }
  });
};

/**
 * 获取角色列表
 * @returns {Promise} - 返回角色列表数据
 */
export const getRoleList = () => {
  return axios.get("/role/list");
};
